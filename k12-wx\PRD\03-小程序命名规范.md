# K12-wx 小程序命名规范

## 1. 总体原则

- 使用英文命名，避免中文拼音
- 采用驼峰命名法（camelCase）或短横线命名法（kebab-case）
- 名称要简洁明了，具有描述性
- 保持项目内命名一致性

## 2. 页面命名规范

### 2.1 页面目录命名
- 使用短横线命名法（kebab-case）
- 名称应简洁且具有描述性

```
pages/
├── index/              # 首页
├── files-list/         # 文件列表页（统一处理年级和升学专区）
├── files-detail/       # 文件详情页
├── category/           # 分类页
├── search/             # 搜索页
├── feedback/           # 反馈页
└── about/              # 关于页
```

### 2.2 页面文件命名
- 与目录名保持一致
- 四个核心文件：`.js`、`.json`、`.wxml`、`.wxss`

```
files-list/
├── files-list.js
├── files-list.json
├── files-list.wxml
└── files-list.wxss
```

## 3. 函数命名规范

### 3.1 页面函数命名
- 使用驼峰命名法（camelCase）
- 动词开头，描述具体功能

```javascript
// 数据获取类
loadFiles()           # 加载文件列表
getFileDetail()       # 获取文件详情
fetchCategories()     # 获取分类数据

// 用户交互类
onFileClick()         # 文件点击事件
onCategorySelect()    # 分类选择事件
onSearchInput()       # 搜索输入事件

// 状态管理类
updateFileList()      # 更新文件列表
setLoading()          # 设置加载状态
resetFilters()        # 重置筛选条件
```

### 3.2 工具函数命名
- 使用驼峰命名法（camelCase）
- 功能描述清晰

```javascript
// API调用类
getFilesList()        # 获取文件列表API
getFileById()         # 根据ID获取文件API
searchFiles()         # 搜索文件API

// 工具类
formatFileSize()      # 格式化文件大小
formatDate()          # 格式化日期
validateForm()        # 表单验证
```

## 4. 接口命名规范

### 4.1 API接口命名
- 使用RESTful风格
- 资源名使用复数形式

```javascript
// 文件相关接口
GET    /files              # 获取文件列表
GET    /files/:id          # 获取单个文件详情
POST   /files              # 创建文件
PUT    /files/:id          # 更新文件
DELETE /files/:id          # 删除文件

// 分类相关接口
GET    /categories         # 获取分类列表
GET    /categories/:id     # 获取分类详情

// 搜索相关接口
GET    /search/files       # 搜索文件
```

### 4.2 模拟API函数命名
```javascript
// utils/api.js 中的函数命名
getFilesList(params)       # 获取文件列表
getFileDetail(id)          # 获取文件详情
searchFiles(keyword)       # 搜索文件
getCategories()            # 获取分类列表
```

## 5. 数据字段命名规范

### 5.1 文件对象字段
```javascript
{
  id: 'string',           # 文件ID
  title: 'string',        # 文件标题
  description: 'string',  # 文件描述
  categoryId: 'string',   # 分类ID
  categoryName: 'string', # 分类名称
  fileType: 'string',     # 文件类型
  fileSize: 'number',     # 文件大小
  downloadUrl: 'string',  # 下载链接
  coverImage: 'string',   # 封面图片
  uploadTime: 'string',   # 上传时间
  downloadCount: 'number', # 下载次数
  isRecommended: 'boolean', # 是否推荐
  tags: 'array'           # 标签数组
}
```

### 5.2 分类对象字段
```javascript
{
  id: 'string',           # 分类ID
  name: 'string',         # 分类名称
  description: 'string',  # 分类描述
  icon: 'string',         # 分类图标
  sortOrder: 'number',    # 排序权重
  isActive: 'boolean'     # 是否启用
}
```

## 6. 组件命名规范

### 6.1 自定义组件
- 使用短横线命名法（kebab-case）
- 名称具有描述性

```
components/
├── file-card/          # 文件卡片组件
├── category-filter/    # 分类筛选组件
├── search-bar/         # 搜索栏组件
└── loading-spinner/    # 加载动画组件
```

## 7. 样式类命名规范

### 7.1 CSS类命名
- 使用短横线命名法（kebab-case）
- 采用BEM命名方式

```css
/* 文件列表相关样式 */
.files-container { }
.files-list { }
.files-item { }
.files-item__title { }
.files-item__meta { }
.files-item--recommended { }

/* 文件详情相关样式 */
.file-detail { }
.file-detail__header { }
.file-detail__content { }
.file-detail__actions { }
```

## 8. 常量命名规范

### 8.1 常量定义
- 使用全大写字母
- 单词间用下划线分隔

```javascript
// 文件类型常量
const FILE_TYPES = {
  PDF: 'pdf',
  DOC: 'doc',
  PPT: 'ppt',
  VIDEO: 'video'
}

// API端点常量
const API_ENDPOINTS = {
  FILES_LIST: '/files',
  FILE_DETAIL: '/files/:id',
  CATEGORIES: '/categories'
}
```

## 9. 实施建议

1. **分阶段实施**：先确定页面命名，再统一函数命名，最后规范数据字段
2. **保持一致性**：确保所有相关文件都遵循统一的命名规范
3. **测试验证**：命名变更后进行全面测试，确保功能正常
4. **文档更新**：及时更新相关技术文档和注释

## 10. 页面架构说明

### 10.1 files-list 页面统一处理
- **年级选择**：通过 `grade` 参数区分不同年级
- **升学专区**：通过 `type` 参数区分幼升小和小升初
- **统一跳转格式**：
  ```javascript
  // 年级跳转
  `/pages/files-list/files-list?grade=${grade}&title=${grade}文件`
  
  // 升学专区跳转
  `/pages/files-list/files-list?type=${type}&title=${title}`
  ```

### 10.2 参数处理规范
```javascript
// files-list.js 中的参数处理
onLoad(options) {
  const { grade, type, title } = options
  if (grade) {
    // 处理年级筛选逻辑
    this.loadFilesByGrade(grade)
  } else if (type) {
    // 处理升学专区逻辑
    this.loadFilesByType(type)
  }
}
```

## 11. 注意事项

- 重命名时要考虑微信小程序的路由配置
- 确保所有页面跳转链接都正确更新
- 注意模拟数据中的字段名称也要同步更新
- 保持代码注释的准确性
- files-list 页面需要根据不同参数展示不同的筛选和内容
