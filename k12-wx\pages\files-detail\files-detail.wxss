/* 资料详情页 - 严格布局控制 */
.container {
  background: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 160rpx;
  overflow-x: hidden;
  overflow-y: auto;
  width: 100%;
  box-sizing: border-box;
}

/* 资料头部信息 - 卡片样式 */
.material-header {
  background: #fff;
  padding: 32rpx;
  margin: 0 0 20rpx 0;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

/* 学科图标 */
.subject-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24rpx;
}

.icon-text {
  font-size: 60rpx;
  margin-bottom: 8rpx;
}

.subject-name {
  color: #64748b;
  font-size: 24rpx;
  font-weight: 500;
}

/* 详情页头部标题 - 居中显示 */
.material-header .material-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.3;
  margin-bottom: 20rpx;
  text-align: center;
}

/* 相关推荐中的标题 - 左对齐 */
.material-list .material-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.3s ease;
  text-align: left;
}

/* 标签 */
.material-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 12rpx;
  margin-bottom: 32rpx;
}

.tag {
  background: #e2e8f0;
  color: #475569;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

/* 统计信息卡片 - 四个部分 */
.stats-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f1f5f9;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex: 1;
  justify-content: center;
}

.stat-icon {
  font-size: 28rpx;
}

.stat-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 26rpx;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
}

.stat-label {
  font-size: 20rpx;
  color: #64748b;
  margin-top: 4rpx;
}

.stat-divider {
  width: 1rpx;
  height: 32rpx;
  background: #e2e8f0;
}

/* 积分统计项特殊样式 */
.stat-points .stat-value {
  color: #ff6b35;
  font-weight: 700;
}

.stat-points .stat-label {
  color: #ff6b35;
}

/* 卡片样式 - 严格约束 */
.section-card {
  background: #fff;
  margin: 0 0 20rpx 0;
  padding: 32rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

.section-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.description-content {
  font-size: 28rpx;
  color: #4a4a4a;
  line-height: 1.6;
}

/* 预览图片区域 - 重新设计 */
.preview-container {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.preview-grid {
  display: flex;
  gap: 12rpx;
  margin-bottom: 16rpx;
  width: 100%;
  box-sizing: border-box;
}

.preview-item {
  flex: 1;
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
  background: #f8fafc;
  aspect-ratio: 210/297 !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  min-width: 120rpx;
  height: auto;
}

.preview-item::before {
  content: '';
  display: block;
  padding-top: 141.43%; /* 297/210 = 1.4143，确保A4竖版比例 */
}

.preview-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
}

.preview-item:active {
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.preview-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 12rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.preview-item:active .preview-overlay {
  opacity: 1;
}

.preview-tip {
  color: #fff;
  font-size: 24rpx;
  text-align: center;
  font-weight: 500;
}

.preview-hint {
  text-align: center;
  color: #64748b;
  font-size: 24rpx;
  margin-top: 8rpx;
  font-weight: 400;
}

/* 预览占位图 - 简化设计 */
.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 24rpx;
  background: #f8fafc;
  border-radius: 8rpx;
  border: 1rpx dashed #cbd5e1;
  text-align: center;
}

.placeholder-icon {
  font-size: 80rpx;
  opacity: 0.3;
  margin-bottom: 16rpx;
  color: #94a3b8;
}

.placeholder-text {
  color: #64748b;
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.placeholder-tip {
  color: #9ca3af;
  font-size: 24rpx;
  line-height: 1.4;
}

/* 相关推荐样式 - 参考首页推荐资料样式 */
.material-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.material-item {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  background: linear-gradient(135deg, #FFFFFF 0%, #FAFBFF 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 2rpx solid rgba(22, 119, 255, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  gap: 12rpx;
}

.material-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 8rpx;
  height: 100%;
  background: linear-gradient(180deg, #1677FF, #69B1FF);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

/* 涟漪效果 */
.material-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.8s ease, height 0.8s ease;
  pointer-events: none;
  z-index: 0;
}

.material-item:active {
  transform: translateY(-4rpx) scale(0.995);
  box-shadow: 0 16rpx 60rpx rgba(22, 119, 255, 0.12);
}

.material-item:active::before {
  transform: scaleY(1);
}

.material-item:active::after {
  width: 400rpx;
  height: 400rpx;
}

/* 标题行 */
.title-row {
  margin-bottom: 8rpx;
}

.material-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.3s ease;
  text-align: left;
}

.material-item:active .material-title {
  color: #1677FF;
}

/* 文件信息行：图标 + 页数 + 大小 + 特征 */
.file-info-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
  flex-wrap: wrap;
}

.file-icon {
  font-size: 24rpx;
  flex-shrink: 0;
}

.file-pages {
  font-size: 22rpx;
  color: #666;
  font-weight: 600;
  flex-shrink: 0;
}

.file-size {
  font-size: 20rpx;
  color: #999;
  font-weight: 500;
  flex-shrink: 0;
}

.feature-tags-inline {
  display: flex;
  flex-wrap: wrap;
  gap: 6rpx;
  flex: 1;
}

.feature-tag-small {
  background: linear-gradient(135deg, #FFF7E6 0%, #FFFBF0 100%);
  color: #FF8C00;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 18rpx;
  font-weight: 600;
  white-space: nowrap;
  text-align: center;
  line-height: 1.2;
  border: 1rpx solid rgba(255, 140, 0, 0.2);
  transition: all 0.2s ease;
}

.feature-tag-small:active {
  transform: scale(0.95);
}

/* 标签行 */
.tags-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 8rpx;
  align-items: center;
}

.category-tag {
  padding: 4rpx 12rpx;
  background: linear-gradient(135deg, #E6F4FF 0%, #F0F7FF 100%);
  color: #1677FF;
  font-size: 22rpx;
  font-weight: 500;
  border-radius: 16rpx;
  border: 1rpx solid rgba(22, 119, 255, 0.15);
  transition: all 0.3s ease;
}

/* 数据积分一行 */
.stats-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
}

.material-stats {
  display: flex;
  align-items: center;
  gap: 24rpx;
  justify-content: flex-start;
}

.stat-item {
  font-size: 24rpx;
  color: #999999;
  display: flex;
  align-items: center;
  gap: 6rpx;
  transition: color 0.3s ease;
  white-space: nowrap;
  min-width: 80rpx;
}

.material-item:active .stat-item {
  color: #1677FF;
}

/* 底部操作栏 - 简洁风格 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 16rpx 20rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}

.download-btn-full {
  width: 100%;
  background: #1677ff;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: none;
  height: 88rpx;
  box-sizing: border-box;
}

.download-btn-full:active {
  background: #0958d9;
  transform: scale(0.98);
}

.download-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  color: #fff;
  width: 100%;
}

.download-main {
  font-size: 28rpx;
  font-weight: 600;
  line-height: 1;
}

.download-tag {
  font-size: 24rpx;
  opacity: 0.9;
  line-height: 1;
}

/* 下载进度样式 */
.download-progress {
  display: flex;
  align-items: center;
  gap: 16rpx;
  width: 100%;
  color: #fff;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #fff;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  color: #fff;
  font-size: 24rpx;
  font-weight: 600;
  min-width: 60rpx;
  text-align: center;
}

/* 广告加载状态 */
.download-btn-full.ad-loading {
  background: #94a3b8;
  cursor: not-allowed;
}

.download-btn-full.ad-loading:active {
  transform: none;
  background: #94a3b8;
}

/* 底部安全区域适配 */
@supports (bottom: env(safe-area-inset-bottom)) {
  .bottom-actions {
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  }
}

/* 骨架屏样式 */
.skeleton-container {
  background: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.skeleton-header {
  background: #fff;
  padding: 32rpx;
  margin-bottom: 20rpx;
}

.skeleton-info {
  padding: 0;
}

.skeleton-title {
  width: 80%;
  height: 40rpx;
  border-radius: 8rpx;
  background: linear-gradient(90deg, #f0f2f5 25%, #e6e8eb 50%, #f0f2f5 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  margin-bottom: 20rpx;
}

.skeleton-tags {
  display: flex;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.skeleton-tag {
  width: 80rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background: linear-gradient(90deg, #f0f2f5 25%, #e6e8eb 50%, #f0f2f5 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-stats {
  display: flex;
  justify-content: space-around;
  background: #f8fafc;
  border-radius: 12rpx;
  padding: 24rpx 0;
  margin-bottom: 24rpx;
}

.skeleton-stat {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  background: linear-gradient(90deg, #f0f2f5 25%, #e6e8eb 50%, #f0f2f5 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-price {
  width: 100%;
  height: 60rpx;
  border-radius: 12rpx;
  background: linear-gradient(90deg, #f0f2f5 25%, #e6e8eb 50%, #f0f2f5 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-section {
  background: #fff;
  margin: 0 0 20rpx 0;
  padding: 32rpx;
}

.skeleton-section-title {
  width: 30%;
  height: 32rpx;
  border-radius: 8rpx;
  background: linear-gradient(90deg, #f0f2f5 25%, #e6e8eb 50%, #f0f2f5 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  margin-bottom: 24rpx;
}

.skeleton-content {
  width: 100%;
  height: 24rpx;
  border-radius: 6rpx;
  background: linear-gradient(90deg, #f0f2f5 25%, #e6e8eb 50%, #f0f2f5 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  margin-bottom: 16rpx;
}

.skeleton-content:last-child {
  width: 70%;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  gap: 24rpx;
}

.empty-icon {
  font-size: 80rpx;
  opacity: 0.3;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
  margin-bottom: 24rpx;
}

.retry-btn {
  background: #1677ff;
  color: #fff;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}

.retry-btn:active {
  background: #0958d9;
  transform: scale(0.95);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .material-stats {
    flex-direction: column;
    gap: 16rpx;
  }
  
  .material-item {
    flex-direction: column;
    text-align: center;
  }
}