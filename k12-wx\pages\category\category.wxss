/* 分类页面样式 - 独立实现，不依赖其他页面样式 */

.container {
  padding: 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 模式切换器样式 */
.mode-switcher {
  display: flex;
  background-color: #fff;
  margin: 0;
  border-bottom: 1px solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.mode-tab {
  flex: 1;
  text-align: center;
  padding: 32rpx 0;
  font-size: 32rpx;
  color: #666;
  background-color: #fff;
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s ease;
}

.mode-tab.active {
  color: #007aff;
  border-bottom-color: #007aff;
  font-weight: 600;
}

.mode-tab:active {
  background-color: #f0f0f0;
}

/* 筛选器区域样式 - 简洁卡片风格，与列表区分 */
.filter-section {
  background-color: #f8f9fa;
  padding: 32rpx;
  border-bottom: 2rpx solid #e9ecef;
  margin-bottom: 16rpx;
}

.filter-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 20rpx;
  margin-right: 16rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  border: 2rpx solid #e9ecef;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.filter-item:last-child {
  margin-right: 0;
}

.filter-item.selected {
  background-color: #ffffff;
  border-color: #1677FF;
  box-shadow: 0 4rpx 16rpx rgba(22, 119, 255, 0.15);
}

.filter-item:active {
  background-color: #f8f9fa;
  transform: scale(0.98);
}

.filter-item.selected:active {
  background-color: #f0f7ff;
}

.filter-text {
  font-size: 28rpx;
  color: #495057;
  flex: 1;
  font-weight: 500;
}

.filter-item.selected .filter-text {
  color: #1677FF;
  font-weight: 600;
}

.filter-arrow {
  font-size: 20rpx;
  color: #adb5bd;
  margin-left: 12rpx;
  transition: transform 0.3s ease;
}

.filter-arrow.rotate {
  transform: rotate(180deg);
}

.filter-item.selected .filter-arrow {
  color: #1677FF;
}

/* 筛选操作区域 */
.filter-actions {
  margin-top: 32rpx;
  display: flex;
  justify-content: flex-end;
}

.reset-btn {
  padding: 20rpx 32rpx;
  background-color: #dc3545;
  color: #fff;
  font-size: 26rpx;
  font-weight: 500;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(220, 53, 69, 0.2);
  transition: all 0.3s ease;
}

.reset-btn:active {
  background-color: #c82333;
  transform: scale(0.95);
}

/* 排序区域样式 - 简洁风格 */
.sort-section {
  display: flex;
  background-color: #ffffff;
  padding: 20rpx 32rpx;
  border-bottom: 1rpx solid #e9ecef;
  margin-bottom: 8rpx;
}

.sort-item {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  margin-right: 24rpx;
  font-size: 28rpx;
  color: #6c757d;
  border-radius: 12rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.sort-item:last-child {
  margin-right: 0;
}

.sort-item.active {
  color: #1677FF;
  background-color: #f0f7ff;
  font-weight: 600;
}

.sort-item:active {
  background-color: #f8f9fa;
  transform: scale(0.95);
}

.sort-arrow {
  margin-left: 8rpx;
  font-size: 20rpx;
}

/* 资料列表样式 - 参考首页推荐资料样式 */
.material-list {
  padding: 24rpx 32rpx;
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.material-item {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  background: linear-gradient(135deg, #FFFFFF 0%, #FAFBFF 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 2rpx solid rgba(22, 119, 255, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  gap: 12rpx;
}

.material-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 8rpx;
  height: 100%;
  background: linear-gradient(180deg, #1677FF, #69B1FF);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

/* 涟漪效果 */
.material-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.8s ease, height 0.8s ease;
  pointer-events: none;
  z-index: 0;
}

.material-item:active {
  transform: translateY(-4rpx) scale(0.995);
  box-shadow: 0 16rpx 60rpx rgba(22, 119, 255, 0.12);
}

.material-item:active::before {
  transform: scaleY(1);
}

.material-item:active::after {
  width: 400rpx;
  height: 400rpx;
}

/* 标题行 */
.title-row {
  margin-bottom: 8rpx;
}

.material-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.3s ease;
}

.material-item:active .material-title {
  color: #1677FF;
}

/* 文件信息行：图标 + 页数 + 大小 + 特征 */
.file-info-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
  flex-wrap: wrap;
}

.file-icon {
  font-size: 24rpx;
  flex-shrink: 0;
}

.file-pages {
  font-size: 22rpx;
  color: #666;
  font-weight: 600;
  flex-shrink: 0;
}

.file-size {
  font-size: 20rpx;
  color: #999;
  font-weight: 500;
  flex-shrink: 0;
}

.feature-tags-inline {
  display: flex;
  flex-wrap: wrap;
  gap: 6rpx;
  flex: 1;
}

.feature-tag-small {
  background: linear-gradient(135deg, #FFF7E6 0%, #FFFBF0 100%);
  color: #FF8C00;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 18rpx;
  font-weight: 600;
  white-space: nowrap;
  text-align: center;
  line-height: 1.2;
  border: 1rpx solid rgba(255, 140, 0, 0.2);
  transition: all 0.2s ease;
}

.feature-tag-small:active {
  transform: scale(0.95);
}

/* 标签行 */
.tags-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 8rpx;
  align-items: center;
}

.category-tag {
  padding: 4rpx 12rpx;
  background: linear-gradient(135deg, #E6F4FF 0%, #F0F7FF 100%);
  color: #1677FF;
  font-size: 22rpx;
  font-weight: 500;
  border-radius: 16rpx;
  border: 1rpx solid rgba(22, 119, 255, 0.15);
  transition: all 0.3s ease;
}

.feature-tag {
  background: linear-gradient(135deg, #FFF7E6 0%, #FFFBF0 100%);
  color: #FF8C00;
  padding: 4rpx 10rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 600;
  white-space: nowrap;
  text-align: center;
  line-height: 1.2;
  border: 1rpx solid rgba(255, 140, 0, 0.2);
  transition: all 0.2s ease;
}

.feature-tag:active {
  transform: scale(0.95);
}

/* 统计行 */
.stats-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.material-stats {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.stat-item {
  font-size: 24rpx;
  color: #999999;
  display: flex;
  align-items: center;
  gap: 4rpx;
  transition: color 0.3s ease;
}

.material-item:active .stat-item {
  color: #1677FF;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e5e5;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx 0;
}

.load-more-text {
  font-size: 26rpx;
  color: #999;
}

/* 弹窗遮罩 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 弹窗内容 */
.popup {
  width: 100%;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

.popup-header {
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  padding: 40rpx 0 24rpx;
  border-bottom: 1px solid #e5e5e5;
  position: relative;
}

.popup-header::after {
  content: '';
  position: absolute;
  top: 16rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 8rpx;
  background-color: #e5e5e5;
  border-radius: 4rpx;
}

.popup-content {
  padding: 0 32rpx 40rpx;
  max-height: 60vh;
  overflow-y: auto;
}

/* 筛选选项 */
.filter-options {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx 0;
}

.filter-option {
  flex: 0 0 auto;
  padding: 20rpx 32rpx;
  margin: 8rpx 16rpx 8rpx 0;
  background-color: #f8f8f8;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  text-align: center;
  transition: all 0.3s ease;
  min-width: 120rpx;
}

.filter-option:active {
  background-color: #e0e0e0;
}

.filter-option.active {
  background-color: #007aff;
  color: #fff;
  border-color: #007aff;
}

.filter-option.active:active {
  background-color: #0056b3;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .filter-item {
    padding: 16rpx 20rpx;
  }
  
  .filter-text {
    font-size: 26rpx;
  }
  
  .material-item {
    padding: 24rpx;
  }
  
  .material-title {
    font-size: 30rpx;
  }
}

/* 深色模式适配（可选） */
@media (prefers-color-scheme: dark) {
  .container {
    background-color: #1a1a1a;
  }
  
  .mode-switcher,
  .filter-section,
  .sort-section,
  .material-item,
  .popup {
    background-color: #2a2a2a;
    border-color: #404040;
  }
  
  .material-title,
  .filter-text,
  .popup-header {
    color: #fff;
  }
  
  .category-tag {
    background-color: #404040;
    color: #ccc;
  }
  
  .filter-item {
    background-color: #404040;
  }
  
  .filter-item.selected {
    background-color: #1a365d;
  }
}