<view class="container">
  <!-- 顶部区域 -->
  <view class="header-section">
    <view class="app-title">小学免费教辅资料</view>
    <view class="app-subtitle">紧跟课表 实时同步 只做精品</view>
    <view class="app-notice">一线教师纯原创资料，侵权必究！</view>
    
    <!-- 搜索框 -->
    <view class="search-box" bindtap="goToSearch" data-test="search">
      <view class="search-icon">🔍</view>
      <text class="search-placeholder">搜索试卷、练习册...</text>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 年级分类导航 -->
    <view class="grade-section">
      <view class="section-header">
        <view class="section-title">选择年级</view>
      </view>
      <view class="grade-grid">
        <view class="grade-item" wx:for="{{gradeList}}" wx:key="index" bindtap="goToGrade" data-grade="{{item.name}}" data-index="{{index}}">
          <view class="grade-icon grade-icon-{{index + 1}}">
            <text class="icon-text">{{item.iconText}}</text>
          </view>
          <text class="grade-text">{{item.name}}</text>
        </view>
      </view>
    </view>

    <!-- 升学专区 -->
    <view class="upgrade-section">
      <view class="section-header">
        <view class="section-title">升学专区</view>
      </view>
      <view class="upgrade-grid">
        <view class="upgrade-item" wx:for="{{upgradeList}}" wx:key="index" bindtap="goToUpgrade" data-type="{{item.type}}" data-index="{{index}}">
          <view class="upgrade-icon upgrade-icon-{{index + 1}}">
            <text class="upgrade-emoji">{{item.emoji}}</text>
          </view>
          <view class="upgrade-content">
            <text class="upgrade-title">{{item.title}}</text>
            <text class="upgrade-subtitle">{{item.subtitle}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 推荐资料 -->
    <view class="recommend-section">
      <view class="section-header">
        <view class="section-title">推荐资料</view>
        <view class="more-btn" bindtap="goToMoreMaterials">更多 ></view>
      </view>
      <view class="material-list">
        <view class="material-item" wx:for="{{hotMaterials}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
          <!-- 简化实用布局 -->
          
          <!-- 标题行 -->
          <view class="title-row">
            <view class="material-title">{{item.title}}</view>
          </view>
          
          <!-- 文件信息行：图标 + 页数 + 大小 + 特征 -->
          <view class="file-info-row">
            <text class="file-icon" wx:if="{{item.fileInfo.type === 'PDF'}}">🗎</text>
            <text class="file-icon" wx:elif="{{item.fileInfo.type === 'DOC' || item.fileInfo.type === 'DOCX'}}">🗋</text>
            <text class="file-icon" wx:elif="{{item.fileInfo.type === 'XLS' || item.fileInfo.type === 'XLSX'}}">🗊</text>
            <text class="file-icon" wx:elif="{{item.fileInfo.type === 'PPT' || item.fileInfo.type === 'PPTX'}}">🗌</text>
            <text class="file-icon" wx:else>🗎</text>
            
            <text class="file-pages" wx:if="{{item.fileInfo.pages}}">{{item.fileInfo.pages}}页</text>
            <text class="file-size" wx:if="{{item.fileInfo.size}}">{{item.fileInfo.size}}</text>
            
            <view class="feature-tags-inline">
              <text class="feature-tag-small" wx:for="{{item.fileInfo.features}}" wx:key="*this">{{item}}</text>
            </view>
          </view>
          
          <!-- 标签行 -->
          <view class="tags-row">
            <text class="category-tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
          </view>
          
          <!-- 数据积分一行 -->
          <view class="stats-row">
            <view class="material-stats">
              <text class="stat-item">⬇ {{item.downloadCount}}</text>
              <text class="stat-item">👁 {{item.viewCount}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>

    <!-- 空状态 -->
    <view class="empty" wx:if="{{!loading && hotMaterials.length === 0}}">
      <view class="empty-icon">📚</view>
      <view class="empty-text">暂无资料</view>
    </view>
  </view>

</view>
