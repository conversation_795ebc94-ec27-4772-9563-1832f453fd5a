<view class="container">
  <!-- 顶部导航栏 -->
  <view class="header">
    <view class="nav-bar">
      <view class="back-btn" bindtap="goBack">
        <text class="back-icon">←</text>
      </view>
      <view class="page-title">{{pageTitle}}</view>
      <view class="search-btn" bindtap="goToSearch">
        <text class="search-icon">🔍</text>
      </view>
    </view>
  </view>

  <!-- 筛选器区域 -->
  <view class="filter-section">
    <view class="filter-row">
      <view class="filter-item" bindtap="showSubjectFilter">
        <text class="filter-text">{{selectedSubject || '科目'}}</text>
        <text class="filter-arrow">▼</text>
      </view>
      <view class="filter-item" bindtap="showVolumeFilter">
        <text class="filter-text">{{selectedVolume || '册别'}}</text>
        <text class="filter-arrow">▼</text>
      </view>
      <view class="filter-item" bindtap="showTypeFilter">
        <text class="filter-text">{{selectedType || '板块'}}</text>
        <text class="filter-arrow">▼</text>
      </view>
    </view>
    
    <!-- 已选筛选条件 -->
    <view class="selected-filters" wx:if="{{hasSelectedFilters}}">
      <view class="filter-tag" wx:for="{{selectedFilters}}" wx:key="type" bindtap="removeFilter" data-type="{{item.type}}">
        <text class="tag-text">{{item.text}}</text>
        <text class="tag-close">×</text>
      </view>
      <view class="clear-all" bindtap="clearAllFilters">
        <text>清空</text>
      </view>
    </view>
    
    <!-- 排序工具 -->
    <view class="sort-section">
      <view class="sort-item {{selectedSort === '更新时间' ? 'active' : ''}}" bindtap="selectSort" data-sort="更新时间">
        <text class="sort-text">更新时间</text>
        <text class="sort-arrow" wx:if="{{selectedSort === '更新时间'}}" bindtap="toggleSortOrder">{{sortOrder === 'desc' ? '↓' : '↑'}}</text>
      </view>
      <view class="sort-item {{selectedSort === '下载量' ? 'active' : ''}}" bindtap="selectSort" data-sort="下载量">
        <text class="sort-text">下载量</text>
        <text class="sort-arrow" wx:if="{{selectedSort === '下载量'}}" bindtap="toggleSortOrder">{{sortOrder === 'desc' ? '↓' : '↑'}}</text>
      </view>
    </view>
  </view>

  <!-- 资料列表 -->
  <view class="material-list">
    <view class="list-header">
      <text class="result-count">共{{totalCount}}个资料</text>
    </view>
    
    <view class="material-item" wx:for="{{materialList}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
      <!-- 第一行：标题 -->
      <view class="title-row">
        <view class="material-title">{{item.title}}</view>
      </view>
      
      <!-- 第二行：文件信息行（图标 + 页数 + 大小 + 特征） -->
      <view class="file-info-row">
        <text class="file-icon" wx:if="{{item.fileInfo.type === 'PDF'}}">🗎</text>
        <text class="file-icon" wx:elif="{{item.fileInfo.type === 'DOC' || item.fileInfo.type === 'DOCX'}}">🗋</text>
        <text class="file-icon" wx:elif="{{item.fileInfo.type === 'XLS' || item.fileInfo.type === 'XLSX'}}">🗊</text>
        <text class="file-icon" wx:elif="{{item.fileInfo.type === 'PPT' || item.fileInfo.type === 'PPTX'}}">🗌</text>
        <text class="file-icon" wx:else>🗎</text>
        
        <text class="file-pages" wx:if="{{item.fileInfo.pages}}">{{item.fileInfo.pages}}页</text>
        <text class="file-size" wx:if="{{item.fileInfo.size}}">{{item.fileInfo.size}}</text>
        
        <view class="feature-tags-inline">
          <text class="feature-tag-small" wx:for="{{item.fileInfo.features}}" wx:key="*this">{{item}}</text>
        </view>
      </view>
      
      <!-- 第三行：标签 -->
      <view class="tags-row">
        <text class="category-tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
      </view>
      
      <!-- 第四行：数据统计 -->
      <view class="stats-row">
        <view class="material-stats">
          <text class="stat-item">⬇ {{item.downloadCount}}</text>
          <text class="stat-item">👁 {{item.viewCount}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-section" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-section" wx:if="{{!loading && materialList.length === 0}}">
    <view class="empty-icon">📚</view>
    <view class="empty-title">暂无资料</view>
    <view class="empty-subtitle">试试调整筛选条件</view>
  </view>

  <!-- 筛选弹窗 -->
  <view class="filter-modal" wx:if="{{showFilterModal}}" bindtap="hideFilterModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">{{currentFilterTitle}}</text>
        <view class="modal-close" bindtap="hideFilterModal">×</view>
      </view>
      <view class="modal-body">
        <view class="filter-option" 
              wx:for="{{currentFilterOptions}}" 
              wx:key="value" 
              bindtap="selectFilterOption" 
              data-value="{{item.value}}"
              data-text="{{item.text}}">
          <text class="option-text {{item.selected ? 'selected' : ''}}">{{item.text}}</text>
          <view class="option-check" wx:if="{{item.selected}}">✓</view>
        </view>
      </view>
    </view>
  </view>
</view>