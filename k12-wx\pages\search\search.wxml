<view class="container">
  <!-- 搜索栏 -->
  <view class="search-header">
    <view class="search-input-box">
      <view class="search-icon">
        <image src="/images/search-icon.png" class="search-icon-img" mode="aspectFit" />
      </view>
      <input 
        class="search-input" 
        placeholder="搜索试卷、练习册、教案..." 
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
        focus="{{searchFocus}}"
        confirm-type="search"
        placeholder-class="search-placeholder"
      />
      <view class="clear-btn" wx:if="{{searchKeyword}}" bindtap="clearSearch">
        <view class="clear-icon">×</view>
      </view>
    </view>
  </view>

  <!-- 搜索建议 -->
  <view class="search-suggestions" wx:if="{{showSuggestions && searchSuggestions.length > 0}}">
    <view class="suggestion-list">
      <view 
        class="suggestion-item" 
        wx:for="{{searchSuggestions}}" 
        wx:key="*this"
        bindtap="selectSuggestion"
        data-keyword="{{item}}"
      >
        <view class="suggestion-icon">🔍</view>
        <text class="suggestion-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 搜索历史 -->
  <view class="search-history" wx:if="{{!searchKeyword && !showSuggestions && searchHistory.length > 0}}">
    <view class="history-header">
      <text class="history-title">搜索历史</text>
      <text class="clear-history" bindtap="clearHistory">清空</text>
    </view>
    <view class="history-tags">
      <text 
        class="history-tag" 
        wx:for="{{searchHistory}}" 
        wx:key="*this"
        bindtap="selectHistory"
        data-keyword="{{item}}"
      >{{item}}</text>
    </view>
  </view>

  <!-- 热门搜索 -->
  <view class="hot-search" wx:if="{{!searchKeyword && !showSuggestions && hotKeywords.length > 0}}">
    <view class="hot-header">
      <text class="hot-title">热门搜索</text>
    </view>
    <view class="hot-tags">
      <text 
        class="hot-tag" 
        wx:for="{{hotKeywords}}" 
        wx:key="*this"
        bindtap="selectHotKeyword"
        data-keyword="{{item}}"
      >{{item}}</text>
    </view>
  </view>

  <!-- 搜索页顶部广告 -->
  <view class="search-ad-top" wx:if="{{searchResults.length > 0 && adConfig.enabled}}">
    <ad unit-id="{{adConfig.ad_id}}" ad-type="{{adConfig.ad_type}}" ad-intervals="30"></ad>
  </view>

  <!-- 搜索结果 - 四行布局 -->
  <view class="search-results" wx:if="{{searchResults.length > 0}}">
    <view class="result-header">
      <text class="result-count">
        {{totalCount > 0 ? '找到 ' + totalCount + ' 个结果' : '未找到相关结果'}}
        <text class="search-keyword" wx:if="{{searchKeyword}}">"{{searchKeyword}}"</text>
      </text>
      <view class="sort-trigger" bindtap="showSortTool">
        <text class="sort-text">{{selectedSort}}</text>
        <text class="sort-arrow">{{sortOrder === 'desc' ? '↓' : '↑'}}</text>
      </view>
    </view>
    <view class="result-list">
      <view 
        class="result-item" 
        wx:for="{{searchResults}}" 
        wx:key="_id"
        bindtap="goToDetail"
        data-id="{{item._id}}"
      >
        <!-- 第一行：标题 -->
        <view class="title-row">
          <view class="material-title">{{item.title}}</view>
        </view>
        
        <!-- 第二行：文件信息行（图标 + 页数 + 大小 + 特征） -->
        <view class="file-info-row">
          <text class="file-icon" wx:if="{{item.fileInfo.type === 'PDF'}}">🗎</text>
          <text class="file-icon" wx:elif="{{item.fileInfo.type === 'DOC' || item.fileInfo.type === 'DOCX'}}">🗋</text>
          <text class="file-icon" wx:elif="{{item.fileInfo.type === 'XLS' || item.fileInfo.type === 'XLSX'}}">🗊</text>
          <text class="file-icon" wx:elif="{{item.fileInfo.type === 'PPT' || item.fileInfo.type === 'PPTX'}}">🗌</text>
          <text class="file-icon" wx:else>🗎</text>
          
          <text class="file-pages" wx:if="{{item.fileInfo.pages}}">{{item.fileInfo.pages}}页</text>
          <text class="file-size" wx:if="{{item.fileInfo.size}}">{{item.fileInfo.size}}</text>
          
          <view class="feature-tags-inline">
            <text class="feature-tag-small" wx:for="{{item.fileInfo.features}}" wx:key="*this">{{item}}</text>
          </view>
        </view>
        
        <!-- 第三行：标签 -->
        <view class="tags-row">
          <text class="category-tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
        </view>
        
        <!-- 第四行：数据统计 -->
        <view class="stats-row">
          <view class="material-stats">
            <text class="stat-item">⬇ {{item.download_count || item.downloads || 0}}</text>
            <text class="stat-item">👁 {{item.view_count || 0}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>搜索中...</text>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{searchResults.length > 0 && !loading}}">
    <view class="load-more-content" wx:if="{{loadingMore}}">
      <text>加载中...</text>
    </view>
    <view class="load-more-content" wx:elif="{{!hasMore}}">
      <text>没有更多了</text>
    </view>
    <view class="load-more-content" wx:else>
      <text>上拉加载更多</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty" wx:if="{{!loading && searchKeyword && searchResults.length === 0}}">
    <view class="empty-icon">🔍</view>
    <view class="empty-text">未找到相关资料</view>
    <view class="empty-desc">试试其他关键词</view>
  </view>

  <!-- 排序工具弹窗 -->
  <view class="sort-modal" wx:if="{{showSortTool}}" bindtap="hideSortTool">
    <view class="sort-content" bindtap="stopPropagation">
      <view class="sort-header">
        <text class="sort-title">排序方式</text>
        <view class="sort-close" bindtap="hideSortTool">×</view>
      </view>
      
      <view class="sort-options">
        <view 
          class="sort-option {{sortType === item.value ? 'active' : ''}}" 
          wx:for="{{sortOptions}}" 
          wx:key="value"
          bindtap="selectSort"
          data-sort="{{item.value}}"
        >
          <view class="sort-option-content">
            <text class="sort-icon">{{item.icon}}</text>
            <text class="sort-name">{{item.text}}</text>
          </view>
          <view class="sort-direction" wx:if="{{sortType === item.value}}" bindtap="toggleSortOrder">
            <text class="direction-text">{{sortOrder === 'desc' ? '降序' : '升序'}}</text>
            <text class="direction-arrow">{{sortOrder === 'desc' ? '↓' : '↑'}}</text>
          </view>
          <view class="sort-direction-inactive" wx:else>
            <text class="direction-text-inactive">{{item.defaultOrder === 'asc' ? '升序' : '降序'}}</text>
            <text class="direction-arrow-inactive">{{item.defaultOrder === 'asc' ? '↑' : '↓'}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

</view>
