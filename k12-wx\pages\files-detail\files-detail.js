// 文件详情页面逻辑
const { cloudApi } = require('../../utils/cloudApi')

Page({
  data: {
    // 文件详情
    materialInfo: null,
    loading: true,
    
    // 相关推荐
    relatedMaterials: [],
    
    // 预览图片
    previewImages: [],
    
    // 下载状态
    downloading: false,
    downloadProgress: 0,
    
    // 广告状态
    adLoading: false,
    rewardVideoAd: null
  },

  onLoad(options) {
    const fileId = options.id
    if (fileId) {
      this.loadFileDetail(fileId)
      this.initRewardVideoAd()
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  onUnload() {
    // 清理广告资源
    if (this.data.rewardVideoAd) {
      this.data.rewardVideoAd.destroy()
    }
  },

  onShareAppMessage() {
    const detail = this.data.materialInfo
    if (detail) {
      return {
        title: detail.title,
        path: `/pages/files-detail/files-detail?id=${detail._id}`,
        imageUrl: detail.preview_images && detail.preview_images.length > 0 ? detail.preview_images[0].url : ''
      }
    }
    return {}
  },

  // 初始化激励视频广告
  initRewardVideoAd() {
    // 这里应该从系统配置中获取广告ID，暂时使用占位符
    const adUnitId = 'adunit-87654321' // 从system_configs表获取
    
    if (wx.createRewardedVideoAd) {
      const rewardVideoAd = wx.createRewardedVideoAd({
        adUnitId: adUnitId
      })
      
      rewardVideoAd.onLoad(() => {
        console.log('激励视频广告加载成功')
      })
      
      rewardVideoAd.onError((err) => {
        console.error('激励视频广告加载失败:', err)
      })
      
      rewardVideoAd.onClose((res) => {
        if (res && res.isEnded) {
          // 用户观看完整视频，开始下载
          this.startDownload()
        } else {
          // 用户中途关闭广告
          wx.showToast({
            title: '请观看完整广告后下载',
            icon: 'none'
          })
        }
      })
      
      this.setData({ rewardVideoAd })
    }
  },

  // 加载文件详情
  async loadFileDetail(fileId) {
    this.setData({ loading: true })
    
    try {
      const result = await cloudApi.getFileDetail(fileId)
      
      if (result && result.success) {
        const detail = result.data
        
        // 处理预览图片
        const previewImages = detail.preview_images || []
        const previewUrls = previewImages.map(img => img.url || img)
        
        // 格式化材料信息
        const materialInfo = {
          id: detail._id,
          title: detail.title,
          description: detail.description || '暂无描述',
          subject: detail.subject,
          grade: detail.grade,
          volume: detail.volume,
          section: detail.section,
          tags: detail.tags || [],
          pages: detail.pages || 0,
          fileSize: this.formatFileSize(detail.file_size),
          downloadCount: detail.download_count || 0,
          viewCount: detail.view_count || 0,
          shareCount: detail.share_count || 0,
          previewImages: previewUrls,
          adRequiredCount: detail.ad_required_count || 1,
          fileType: detail.file_type || 'PDF',
          fileUrl: detail.file_url
        }
        
        this.setData({
          materialInfo,
          previewImages: previewUrls
        })
        
        // 设置页面标题
        wx.setNavigationBarTitle({
          title: detail.title.length > 10 ? detail.title.substring(0, 10) + '...' : detail.title
        })
        
        // 加载相关推荐
        this.loadRelatedFiles(detail)
        
      } else {
        wx.showToast({
          title: '文件不存在',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
      
    } catch (error) {
      console.error('加载文件详情失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载相关推荐
  async loadRelatedFiles(currentFile) {
    try {
      // 查询相同学科和年级的其他文件作为推荐
      const result = await cloudApi.getFilesList({
        grade: currentFile.grade,
        subject: currentFile.subject,
        pageSize: 6
      })
      
      if (result.success) {
        // 过滤掉当前文件，并格式化数据（参考首页数据格式）
        const relatedMaterials = result.data
          .filter(item => item._id !== currentFile._id)
          .slice(0, 5)
          .map(item => ({
            id: item._id,
            title: item.title,
            tags: Array.isArray(item.tags) ? item.tags.slice(0, 4) : [], // 直接使用数据库tags字段
            features: Array.isArray(item.features) ? item.features : [], // 文件特征
            downloadCount: item.download_count || 0,
            viewCount: item.view_count || 0,
            pages: item.pages || 0,
            size: this.formatFileSize(item.file_size),
            fileType: item.file_type || 'PDF'
          }))
        
        this.setData({ relatedMaterials })
      }
      
    } catch (error) {
      console.error('加载相关推荐失败:', error)
    }
  },

  // 格式化文件大小
  formatFileSize(size) {
    if (!size || size <= 0) return '未知'
    if (size > 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + 'MB'
    if (size > 1024) return (size / 1024).toFixed(1) + 'KB'
    return size + 'B'
  },

  // 预览图片
  previewImage(e) {
    const index = e.currentTarget.dataset.index || 0
    const images = this.data.previewImages
    
    if (images.length > 0) {
      wx.previewImage({
        current: images[index],
        urls: images
      })
    }
  },

  // 下载资源（点击下载按钮）
  async downloadResource() {
    const materialInfo = this.data.materialInfo
    if (!materialInfo) return
    
    if (this.data.downloading) return
    
    // 检查是否需要观看广告
    const adRequiredCount = materialInfo.adRequiredCount || 1
    
    if (adRequiredCount > 0 && this.data.rewardVideoAd) {
      // 需要观看激励视频广告
      this.setData({ adLoading: true })
      
      try {
        await this.data.rewardVideoAd.show()
      } catch (error) {
        console.error('显示激励视频广告失败:', error)
        // 广告加载失败，直接下载
        this.startDownload()
      } finally {
        this.setData({ adLoading: false })
      }
    } else {
      // 不需要广告，直接下载
      this.startDownload()
    }
  },

  // 开始下载
  async startDownload() {
    const materialInfo = this.data.materialInfo
    if (!materialInfo) return
    
    this.setData({ 
      downloading: true,
      downloadProgress: 0
    })
    
    try {
      // 模拟下载进度
      const progressInterval = setInterval(() => {
        const currentProgress = this.data.downloadProgress
        if (currentProgress < 90) {
          this.setData({
            downloadProgress: currentProgress + Math.random() * 20
          })
        }
      }, 200)
      
      // 模拟下载延迟
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      clearInterval(progressInterval)
      this.setData({ downloadProgress: 100 })
      
      // 更新下载次数
      await cloudApi.updateDownloadCount(materialInfo.id)
      
      wx.showToast({
        title: '下载完成',
        icon: 'success'
      })
      
      // 更新本地显示的下载次数
      this.setData({
        'materialInfo.downloadCount': materialInfo.downloadCount + 1
      })
      
    } catch (error) {
      console.error('下载失败:', error)
      wx.showToast({
        title: '下载失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ 
        downloading: false,
        downloadProgress: 0
      })
    }
  },

  // 跳转到相关文件详情
  goToRelated(e) {
    const id = e.currentTarget.dataset.id
    wx.redirectTo({
      url: `/pages/files-detail/files-detail?id=${id}`
    })
  }
})
