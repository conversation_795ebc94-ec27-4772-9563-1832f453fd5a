// 分类页面逻辑 - 使用独立API接口
const { 
  getCategoryFilterOptions, 
  getCategoryRegularFiles, 
  getCategoryUpgradeFiles, 
  getCategoryUpgradeOptions 
} = require('../../utils/api/categoryApi')

const { 
  processCategoryFileData, 
  processCategoryUpgradeData,
  hasCategoryFilters,
  clearCategoryFilters
} = require('../../utils/helpers/categoryHelpers')

Page({
  data: {
    // 模式切换
    currentMode: 'normal', // 'normal' | 'upgrade'
    
    // 筛选条件
    normalFilters: {
      selectedGrade: '',
      selectedSubject: '',
      selectedVolume: '',
      selectedSection: ''
    },
    
    upgradeFilters: {
      selectedUpgradeType: '',
      selectedCategory: ''
    },
    
    // 排序条件
    sortType: 'download_count',
    sortOrder: 'desc',
    
    // 筛选选项
    gradeOptions: [],
    subjectOptions: [],
    volumeOptions: [],
    sectionOptions: [],
    upgradeTypeOptions: ['幼升小', '小升初'],
    upgradeCategoryOptions: [],
    
    // 弹窗状态
    showGradePopup: false,
    showSubjectPopup: false,
    showVolumePopup: false,
    showSectionPopup: false,
    showUpgradeTypePopup: false,
    showUpgradeCategoryPopup: false,
    
    // 文件列表
    filesList: [],
    loading: false,
    page: 1,
    hasMore: true
  },

  onLoad(options) {
    // 处理传入的筛选条件
    if (options.grade) {
      if (options.grade === '幼升小' || options.grade === '小升初') {
        this.setData({ 
          currentMode: 'upgrade',
          'upgradeFilters.selectedUpgradeType': options.grade
        })
      } else {
        this.setData({ 
          currentMode: 'normal',
          'normalFilters.selectedGrade': options.grade
        })
      }
    }
    if (options.subject) {
      this.setData({ 'normalFilters.selectedSubject': options.subject })
    }
    
    wx.setNavigationBarTitle({ title: '文件分类' })
    
    this.loadFilterOptions().then(() => {
      this.loadFiles()
    })
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadFiles()
    }
  },

  onPullDownRefresh() {
    this.refreshData()
  },

  // 切换模式
  switchMode(e) {
    const mode = e.currentTarget.dataset.mode
    if (mode === this.data.currentMode) return
    
    this.setData({
      currentMode: mode,
      page: 1,
      filesList: [],
      hasMore: true
    })
    
    this.loadFilterOptions().then(() => {
      this.loadFiles()
    })
    
    wx.showToast({
      title: mode === 'normal' ? '切换到常规文件' : '切换到升学专区',
      icon: 'none'
    })
  },

  // 刷新数据
  refreshData() {
    this.setData({
      page: 1,
      filesList: [],
      hasMore: true
    })
    this.loadFilterOptions().then(() => {
      this.loadFiles().then(() => {
        wx.stopPullDownRefresh()
      }).catch(() => {
        wx.stopPullDownRefresh()
      })
    })
  },

  // 加载筛选选项
  async loadFilterOptions() {
    try {
      if (this.data.currentMode === 'normal') {
        const result = await getCategoryFilterOptions('regular')
        
        if (result && result.success) {
          const data = result.data
          this.setData({
            gradeOptions: data.grades || [],
            subjectOptions: data.subjects || [],
            volumeOptions: data.volumes || [],
            sectionOptions: data.sections || []
          })
        }
      } else {
        const result = await getCategoryUpgradeOptions()
        
        if (result && result.success) {
          const data = result.data
          this.setData({
            upgradeTypeOptions: data.upgradeTypes || ['幼升小', '小升初'],
            upgradeCategoryOptions: data.sections || []
          })
        }
      }
    } catch (error) {
      console.error('加载筛选选项失败:', error)
      // 使用默认选项
      if (this.data.currentMode === 'normal') {
        this.setData({
          gradeOptions: ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级'],
          subjectOptions: ['语文', '数学', '英语', '科学', '道德与法治'],
          volumeOptions: ['上册', '下册', '全册'],
          sectionOptions: ['单元同步', '单元知识点', '期中试卷', '期末试卷', '练习册', '课件资料']
        })
      } else {
        this.setData({
          upgradeTypeOptions: ['幼升小', '小升初'],
          upgradeCategoryOptions: [
            '拼音启蒙', '认识数字', '习惯养成', '学科启蒙', '知识科普',
            '语文冲刺', '数学冲刺', '英语强化', '真题模拟', '面试准备'
          ]
        })
      }
    }
  },

  // 加载文件列表
  async loadFiles() {
    if (this.data.loading) return
    
    this.setData({ loading: true })
    
    try {
      let result
      
      if (this.data.currentMode === 'normal') {
        const params = {
          page: this.data.page,
          pageSize: 20,
          grade: this.data.normalFilters.selectedGrade,
          subject: this.data.normalFilters.selectedSubject,
          volume: this.data.normalFilters.selectedVolume,
          section: this.data.normalFilters.selectedSection,
          sortBy: this.data.sortType,
          sortOrder: this.data.sortOrder
        }
        
        result = await getCategoryRegularFiles(params)
      } else {
        const params = {
          page: this.data.page,
          pageSize: 20,
          upgradeType: this.data.upgradeFilters.selectedUpgradeType,
          section: this.data.upgradeFilters.selectedCategory,
          sortBy: this.data.sortType,
          sortOrder: this.data.sortOrder
        }
        
        result = await getCategoryUpgradeFiles(params)
      }
      
      if (result && result.success) {
        let processedData
        
        if (this.data.currentMode === 'normal') {
          processedData = processCategoryFileData(result.data || [])
        } else {
          processedData = processCategoryUpgradeData(result.data || [])
        }
        
        const newList = this.data.page === 1 ? processedData : [...this.data.filesList, ...processedData]
        
        this.setData({
          filesList: newList,
          hasMore: result.hasMore !== false,
          page: this.data.page + 1
        })
      } else {
        if (this.data.page === 1) {
          this.setData({ filesList: [] })
        }
        wx.showToast({
          title: '暂无数据',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载文件列表失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 筛选器显示/隐藏方法
  showGradeFilter() { this.setData({ showGradePopup: true }) },
  showSubjectFilter() { this.setData({ showSubjectPopup: true }) },
  showVolumeFilter() { this.setData({ showVolumePopup: true }) },
  showSectionFilter() { this.setData({ showSectionPopup: true }) },
  showUpgradeTypeFilter() { this.setData({ showUpgradeTypePopup: true }) },
  showUpgradeCategoryFilter() { this.setData({ showUpgradeCategoryPopup: true }) },

  hideGradeFilter() { this.setData({ showGradePopup: false }) },
  hideSubjectFilter() { this.setData({ showSubjectPopup: false }) },
  hideVolumeFilter() { this.setData({ showVolumePopup: false }) },
  hideSectionFilter() { this.setData({ showSectionPopup: false }) },
  hideUpgradeTypeFilter() { this.setData({ showUpgradeTypePopup: false }) },
  hideUpgradeCategoryFilter() { this.setData({ showUpgradeCategoryPopup: false }) },

  // 筛选条件选择方法
  selectGrade(e) {
    this.selectFilter('normalFilters.selectedGrade', e.currentTarget.dataset.grade, 'showGradePopup')
  },

  selectSubject(e) {
    this.selectFilter('normalFilters.selectedSubject', e.currentTarget.dataset.subject, 'showSubjectPopup')
  },

  selectVolume(e) {
    this.selectFilter('normalFilters.selectedVolume', e.currentTarget.dataset.volume, 'showVolumePopup')
  },

  selectSection(e) {
    this.selectFilter('normalFilters.selectedSection', e.currentTarget.dataset.section, 'showSectionPopup')
  },

  selectUpgradeType(e) {
    this.selectFilter('upgradeFilters.selectedUpgradeType', e.currentTarget.dataset.type, 'showUpgradeTypePopup')
  },

  selectUpgradeCategory(e) {
    this.selectFilter('upgradeFilters.selectedCategory', e.currentTarget.dataset.category, 'showUpgradeCategoryPopup')
  },

  // 通用筛选选择方法
  selectFilter(filterKey, value, popupKey) {
    this.setData({
      [filterKey]: value,
      [popupKey]: false,
      page: 1,
      filesList: [],
      hasMore: true
    })
    this.loadFiles()
  },

  // 清除筛选条件方法
  clearGrade() { this.clearFilter('normalFilters.selectedGrade') },
  clearSubject() { this.clearFilter('normalFilters.selectedSubject') },
  clearVolume() { this.clearFilter('normalFilters.selectedVolume') },
  clearSection() { this.clearFilter('normalFilters.selectedSection') },
  clearUpgradeType() { this.clearFilter('upgradeFilters.selectedUpgradeType') },
  clearUpgradeCategory() { this.clearFilter('upgradeFilters.selectedCategory') },

  // 通用清除筛选方法
  clearFilter(filterKey) {
    this.setData({
      [filterKey]: '',
      page: 1,
      filesList: [],
      hasMore: true
    })
    this.loadFiles()
  },

  // 清除所有筛选条件
  clearAllFilters() {
    const clearedFilters = clearCategoryFilters(this.data.currentMode)
    
    if (this.data.currentMode === 'normal') {
      this.setData({
        normalFilters: clearedFilters,
        page: 1,
        filesList: [],
        hasMore: true
      })
    } else {
      this.setData({
        upgradeFilters: clearedFilters,
        page: 1,
        filesList: [],
        hasMore: true
      })
    }
    this.loadFiles()
  },

  // 重置筛选条件（WXML中使用的方法名）
  resetFilters() {
    this.clearAllFilters()
  },

  resetUpgradeFilters() {
    this.clearAllFilters()
  },

  // 排序方法
  setSortType(e) {
    const sortType = e.currentTarget.dataset.type
    
    // 转换排序类型
    let actualSortType = sortType
    if (sortType === 'download') {
      actualSortType = 'download_count'
    } else if (sortType === 'time') {
      actualSortType = 'created_time'
    }
    
    if (this.data.sortType === actualSortType) {
      const sortOrder = this.data.sortOrder === 'desc' ? 'asc' : 'desc'
      this.setData({ sortOrder })
    } else {
      this.setData({
        sortType: actualSortType,
        sortOrder: 'desc'
      })
    }
    
    this.setData({
      page: 1,
      filesList: [],
      hasMore: true
    })
    this.loadFiles()
  },

  // 跳转到文件详情
  goToDetail(e) {
    const fileId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/files-detail/files-detail?id=${fileId}&from=category`
    })
  }
})