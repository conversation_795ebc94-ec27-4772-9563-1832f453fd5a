/**
 * 搜索页专用API接口
 * 严格按照项目解耦规则：仅供搜索页使用，其他页面禁止调用
 */

const db = wx.cloud.database()

/**
 * 智能搜索文件 - 多级优先级排序
 */
const searchFiles = async (params = {}) => {
  try {
    const collection = db.collection('files')
    
    if (!params.keyword || !params.keyword.trim()) {
      return {
        success: false,
        error: '搜索关键词不能为空',
        data: {
          list: [],
          pagination: {
            total: 0,
            page: 1,
            pageSize: 20,
            hasMore: false
          }
        }
      }
    }

    const keyword = params.keyword.trim()
    const keywords = keyword.split(/\s+/).filter(k => k.length > 0) // 分词处理
    
    // 获取所有可能匹配的文件
    const allResults = await getAllMatchingFiles(collection, keyword, keywords)
    
    if (allResults.length === 0) {
      return {
        success: true,
        data: {
          list: [],
          pagination: {
            total: 0,
            page: 1,
            pageSize: 20,
            hasMore: false
          }
        }
      }
    }

    // 计算每个文件的匹配优先级分数
    const scoredResults = allResults.map(file => ({
      ...file,
      _searchScore: calculateSearchScore(file, keyword, keywords)
    }))

    // 按优先级分数和用户选择的排序方式排序
    const sortBy = params.sortBy || 'sort_order'
    const sortOrder = params.sortOrder || 'desc'
    
    scoredResults.sort((a, b) => {
      // 如果是推荐排序，优先按搜索匹配分数排序
      if (sortBy === 'sort_order') {
        if (b._searchScore !== a._searchScore) {
          return b._searchScore - a._searchScore
        }
        // 搜索分数相同时按sort_order排序
        const compareValue = (b.sort_order || 0) - (a.sort_order || 0)
        return sortOrder === 'desc' ? compareValue : -compareValue
      }
      
      // 其他排序方式：主要按用户选择的排序方式，搜索分数作为次要排序
      let compareValue = 0
      switch (sortBy) {
        case 'download_count':
          compareValue = (b.download_count || 0) - (a.download_count || 0)
          break
        case 'view_count':
          compareValue = (b.view_count || 0) - (a.view_count || 0)
          break
        case 'created_time':
          compareValue = new Date(b.created_time || 0) - new Date(a.created_time || 0)
          break
      }
      
      // 主排序字段相同时，按搜索分数排序
      if (compareValue === 0) {
        return b._searchScore - a._searchScore
      }
      
      return sortOrder === 'desc' ? compareValue : -compareValue
    })

    // 分页处理
    const page = params.page || 1
    const pageSize = params.pageSize || 20
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedResults = scoredResults.slice(startIndex, endIndex)

    // 移除搜索分数字段
    const finalResults = paginatedResults.map(file => {
      const { _searchScore, ...fileWithoutScore } = file
      return fileWithoutScore
    })

    return {
      success: true,
      data: {
        list: finalResults,
        pagination: {
          total: scoredResults.length,
          page: page,
          pageSize: pageSize,
          hasMore: endIndex < scoredResults.length
        }
      }
    }
    
  } catch (error) {
    console.error('搜索文件失败:', error)
    return {
      success: false,
      error: error.message,
      data: {
        list: [],
        pagination: {
          total: 0,
          page: 1,
          pageSize: 20,
          hasMore: false
        }
      }
    }
  }
}

/**
 * 获取所有可能匹配的文件
 */
const getAllMatchingFiles = async (collection, keyword, keywords) => {
  const allResults = []
  const processedIds = new Set()

  // 1. 精确匹配整个关键词
  const exactResults = await collection
    .where({
      status: 'active',
      $or: [
        { title: db.RegExp({ regexp: keyword, options: 'i' }) },
        { description: db.RegExp({ regexp: keyword, options: 'i' }) },
        { subject: db.RegExp({ regexp: keyword, options: 'i' }) },
        { grade: db.RegExp({ regexp: keyword, options: 'i' }) },
        { section: db.RegExp({ regexp: keyword, options: 'i' }) },
        { tags: db.RegExp({ regexp: keyword, options: 'i' }) }
      ]
    })
    .get()

  exactResults.data.forEach(file => {
    if (!processedIds.has(file._id)) {
      allResults.push(file)
      processedIds.add(file._id)
    }
  })

  // 2. 分词匹配
  for (const word of keywords) {
    if (word.length >= 2) { // 只处理长度>=2的词
      const wordResults = await collection
        .where({
          status: 'active',
          $or: [
            { title: db.RegExp({ regexp: word, options: 'i' }) },
            { description: db.RegExp({ regexp: word, options: 'i' }) },
            { subject: db.RegExp({ regexp: word, options: 'i' }) },
            { grade: db.RegExp({ regexp: word, options: 'i' }) },
            { section: db.RegExp({ regexp: word, options: 'i' }) },
            { tags: db.RegExp({ regexp: word, options: 'i' }) }
          ]
        })
        .get()

      wordResults.data.forEach(file => {
        if (!processedIds.has(file._id)) {
          allResults.push(file)
          processedIds.add(file._id)
        }
      })
    }
  }

  return allResults
}

/**
 * 计算搜索匹配分数
 */
const calculateSearchScore = (file, keyword, keywords) => {
  let score = 0
  const keywordLower = keyword.toLowerCase()
  
  // 标题匹配权重最高
  const titleLower = (file.title || '').toLowerCase()
  if (titleLower.includes(keywordLower)) {
    score += 1000 // 完整关键词匹配标题
    if (titleLower.startsWith(keywordLower)) {
      score += 500 // 标题开头匹配
    }
  }
  
  // 分词匹配标题
  let titleWordMatches = 0
  keywords.forEach(word => {
    if (word.length >= 2 && titleLower.includes(word.toLowerCase())) {
      titleWordMatches++
      score += 300 // 每个词匹配标题
    }
  })
  
  // 连续匹配加分
  if (titleWordMatches === keywords.length && keywords.length > 1) {
    score += 200 // 所有词都匹配标题
  }

  // 描述匹配
  const descriptionLower = (file.description || '').toLowerCase()
  if (descriptionLower.includes(keywordLower)) {
    score += 250 // 完整关键词匹配描述
  }
  keywords.forEach(word => {
    if (word.length >= 2 && descriptionLower.includes(word.toLowerCase())) {
      score += 100 // 每个词匹配描述
    }
  })

  // 学科匹配
  const subjectLower = (file.subject || '').toLowerCase()
  if (subjectLower.includes(keywordLower)) {
    score += 400 // 完整关键词匹配学科
  }
  keywords.forEach(word => {
    if (word.length >= 2 && subjectLower.includes(word.toLowerCase())) {
      score += 150 // 每个词匹配学科
    }
  })

  // 年级匹配
  const gradeLower = (file.grade || '').toLowerCase()
  if (gradeLower.includes(keywordLower)) {
    score += 300 // 完整关键词匹配年级
  }
  keywords.forEach(word => {
    if (word.length >= 2 && gradeLower.includes(word.toLowerCase())) {
      score += 120 // 每个词匹配年级
    }
  })

  // 板块匹配
  const sectionLower = (file.section || '').toLowerCase()
  if (sectionLower.includes(keywordLower)) {
    score += 200 // 完整关键词匹配板块
  }
  keywords.forEach(word => {
    if (word.length >= 2 && sectionLower.includes(word.toLowerCase())) {
      score += 80 // 每个词匹配板块
    }
  })

  // 标签匹配
  if (Array.isArray(file.tags)) {
    const tagsText = file.tags.join(' ').toLowerCase()
    if (tagsText.includes(keywordLower)) {
      score += 150 // 完整关键词匹配标签
    }
    keywords.forEach(word => {
      if (word.length >= 2 && tagsText.includes(word.toLowerCase())) {
        score += 50 // 每个词匹配标签
      }
    })
  }

  // 热门度加分
  if (file.sort_order >= 1000) {
    score += 100 // 置顶文件
  } else if (file.sort_order >= 500) {
    score += 50 // 热门文件
  } else if (file.sort_order >= 100) {
    score += 25 // 推荐文件
  }

  // 下载量加分
  const downloadCount = file.download_count || 0
  if (downloadCount > 1000) {
    score += 30
  } else if (downloadCount > 100) {
    score += 15
  } else if (downloadCount > 10) {
    score += 5
  }

  return score
}

/**
 * 获取热门搜索关键词
 */
const getHotKeywords = async () => {
  try {
    const result = await db.collection('system_configs')
      .where({
        key: 'hot_keywords',
        category: 'search'
      })
      .get()
    
    if (result.data.length > 0) {
      const config = result.data[0]
      const keywords = config.value.split(',').map(k => k.trim()).filter(k => k)
      
      return {
        success: true,
        data: keywords
      }
    } else {
      // 返回默认热门关键词
      return {
        success: true,
        data: ['期末试卷', '单元测试', '练习册', '知识点总结', '作文素材']
      }
    }
    
  } catch (error) {
    console.error('获取热门搜索关键词失败:', error)
    // 返回默认热门关键词
    return {
      success: true,
      data: ['期末试卷', '单元测试', '练习册', '知识点总结', '作文素材']
    }
  }
}

/**
 * 获取搜索建议
 */
const getSearchSuggestions = async (keyword) => {
  try {
    if (!keyword || keyword.length < 2) {
      return {
        success: true,
        data: []
      }
    }
    
    // 从文件标题中获取搜索建议
    const result = await db.collection('files')
      .where({
        status: 'active',
        title: db.RegExp({
          regexp: keyword,
          options: 'i'
        })
      })
      .field({
        title: true
      })
      .limit(10)
      .get()
    
    // 提取关键词相关的建议
    const suggestions = result.data
      .map(item => item.title)
      .filter(title => title.toLowerCase().includes(keyword.toLowerCase()))
      .slice(0, 5)
    
    return {
      success: true,
      data: [...new Set(suggestions)] // 去重
    }
    
  } catch (error) {
    console.error('获取搜索建议失败:', error)
    return {
      success: true,
      data: []
    }
  }
}

/**
 * 获取搜索页广告配置
 */
const getSearchAdConfig = async () => {
  try {
    const result = await db.collection('system_configs')
      .where({
        key: 'ad_search_top',
        category: 'ads'
      })
      .get()
    
    if (result.data.length > 0) {
      const config = JSON.parse(result.data[0].value)
      return {
        success: true,
        data: config
      }
    } else {
      return {
        success: true,
        data: {
          enabled: false
        }
      }
    }
    
  } catch (error) {
    console.error('获取搜索页广告配置失败:', error)
    return {
      success: true,
      data: {
        enabled: false
      }
    }
  }
}

module.exports = {
  searchFiles,
  getHotKeywords,
  getSearchSuggestions,
  getSearchAdConfig
}