// 首页逻辑
Page({
  data: {
    // 年级列表
    gradeList: [
      { name: '一年级', iconText: '启蒙' },
      { name: '二年级', iconText: '基础' },
      { name: '三年级', iconText: '成长' },
      { name: '四年级', iconText: '进阶' },
      { name: '五年级', iconText: '提升' },
      { name: '六年级', iconText: '冲刺' }
    ],

    // 升学专区列表
    upgradeList: [
      { title: '幼升小', subtitle: '入学准备', type: 'kindergarten', emoji: '🎒' },
      { title: '小升初', subtitle: '升学冲刺', type: 'primary', emoji: '🎓' }
    ],
    
    // 热门资料列表
    hotMaterials: [],
    loading: false
  },

  onLoad() {
    this.loadHotFiles()
  },

  onShow() {
    if (this.data.hotMaterials.length === 0) {
      this.loadHotFiles()
    }
  },

  onPullDownRefresh() {
    this.loadHotFiles().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载热门文件
  async loadHotFiles() {
    this.setData({ loading: true })
    
    try {
      const { cloudApi } = require('../../utils/cloudApi')
      const result = await cloudApi.getHotFiles({ pageSize: 8 })
      
      if (result && result.success) {
        const processedData = (result.data || []).map(item => ({
          id: item._id,
          title: item.title,
          tags: Array.isArray(item.tags) ? item.tags.slice(0, 4) : [],
          downloadCount: item.download_count || 0,
          viewCount: item.view_count || 0,
          fileInfo: this.processFileInfo(item),
          subject: item.subject,
          grade: item.grade,
          volume: item.volume,
          section: item.section
        }))
        
        this.setData({ hotMaterials: processedData })
      } else {
        this.setData({ hotMaterials: [] })
        wx.showToast({
          title: '暂无热门文件',
          icon: 'none'
        })
      }
      
    } catch (error) {
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
      this.setData({ hotMaterials: [] })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 处理文件信息
  processFileInfo(item) {
    return {
      type: item.file_type || 'PDF',
      pages: item.pages || 0,
      size: this.formatFileSize(item.file_size),
      features: Array.isArray(item.features) ? item.features : []
    }
  },

  // 格式化文件大小
  formatFileSize(size) {
    if (!size || size <= 0) return ''
    if (size > 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + 'MB'
    if (size > 1024) return (size / 1024).toFixed(1) + 'KB'
    return size + 'B'
  },

  // 页面跳转方法
  goToSearch() {
    wx.switchTab({ url: '/pages/search/search' })
  },

  goToGrade(e) {
    const grade = e.currentTarget.dataset.grade
    wx.navigateTo({
      url: `/pages/files-list/files-list?grade=${grade}&title=${grade}文件`
    })
  },

  goToUpgrade(e) {
    const type = e.currentTarget.dataset.type
    let grade, title
    
    if (type === 'kindergarten') {
      grade = '幼升小'
      title = '幼升小专区'
    } else if (type === 'primary') {
      grade = '小升初'
      title = '小升初专区'
    }
    
    wx.navigateTo({
      url: `/pages/files-list/files-list?grade=${grade}&title=${title}`
    })
  },

  goToDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({ url: `/pages/files-detail/files-detail?id=${id}` })
  },

  goToMoreMaterials() {
    wx.switchTab({ url: '/pages/category/category' })
  }
})