/**
 * 首页专用API接口
 * 严格按照项目解耦规则：仅供首页使用，其他页面禁止调用
 */

const db = wx.cloud.database()

/**
 * 获取首页轮播图数据
 */
const getHomeBanners = async () => {
  try {
    const result = await db.collection('files')
      .where({
        status: 'active',
        sort_order: db.command.gte(1000) // 置顶级别的作为轮播图
      })
      .orderBy('sort_order', 'desc')
      .limit(5)
      .field({
        _id: true,
        title: true,
        preview_images: true,
        sort_order: true
      })
      .get()
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    console.error('获取首页轮播图失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 获取首页推荐文件列表
 */
const getHomeRecommendFiles = async () => {
  try {
    const result = await db.collection('files')
      .where({
        status: 'active',
        sort_order: db.command.gte(100) // 推荐级别
      })
      .orderBy('sort_order', 'desc')
      .orderBy('created_time', 'desc')
      .limit(10)
      .get()
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    console.error('获取首页推荐文件失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 获取首页热门下载
 */
const getHomeHotFiles = async () => {
  try {
    const result = await db.collection('files')
      .where({
        status: 'active'
      })
      .orderBy('download_count', 'desc')
      .limit(8)
      .get()
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    console.error('获取首页热门文件失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 获取首页各年级精选（按年级分组）
 */
const getHomeGradeFiles = async () => {
  try {
    const grades = ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级']
    const gradeFiles = {}
    
    for (const grade of grades) {
      const result = await db.collection('files')
        .where({
          status: 'active',
          category: 'regular',
          grade: grade
        })
        .orderBy('download_count', 'desc')
        .limit(4)
        .get()
      
      gradeFiles[grade] = result.data
    }
    
    return {
      success: true,
      data: gradeFiles
    }
  } catch (error) {
    console.error('获取首页年级文件失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

module.exports = {
  getHomeBanners,
  getHomeRecommendFiles,
  getHomeHotFiles,
  getHomeGradeFiles
}