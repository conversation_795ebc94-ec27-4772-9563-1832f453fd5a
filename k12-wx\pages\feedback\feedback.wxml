<!--pages/feedback/feedback.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">意见反馈</text>
    <text class="page-subtitle">您的建议是我们改进的动力</text>
  </view>

  <!-- 反馈表单 -->
  <view class="feedback-form">
    <!-- 反馈类型 -->
    <view class="form-section">
      <view class="section-title">反馈类型</view>
      <picker mode="selector" range="{{feedbackTypes}}" range-key="label" value="{{feedbackType}}" bindchange="onTypeChange">
        <view class="picker-container">
          <text class="picker-text">{{currentTypeLabel}}</text>
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
    </view>

    <!-- 反馈标题 -->
    <view class="form-section">
      <view class="section-title">反馈标题 <text class="required">*</text></view>
      <input 
        class="input-field" 
        placeholder="请简要描述问题或建议（至少5个字符）"
        value="{{title}}"
        maxlength="50"
        bindinput="onTitleInput"
      />
      <view class="input-counter">{{title.length}}/50</view>
    </view>

    <!-- 反馈内容 -->
    <view class="form-section">
      <view class="section-title">详细描述 <text class="required">*</text></view>
      <textarea 
        class="textarea-field" 
        placeholder="请详细描述您遇到的问题或建议，包括操作步骤、期望结果等（至少10个字符）"
        value="{{content}}"
        maxlength="500"
        bindinput="onContentInput"
        auto-height
      ></textarea>
      <view class="input-counter">{{content.length}}/500</view>
    </view>

    <!-- 联系方式 -->
    <view class="form-section">
      <view class="section-title">邮箱地址 <text class="optional">（选填）</text></view>
      <input 
        class="input-field" 
        placeholder="请输入您的邮箱地址，方便我们回复"
        value="{{email}}"
        maxlength="50"
        bindinput="onEmailInput"
        type="email"
      />
      <view class="input-hint">我们会对您的邮箱地址严格保密</view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <view class="secondary-button" bindtap="resetForm">
      <text>重置</text>
    </view>
    
    <view class="primary-button {{submitting ? 'disabled' : ''}}" bindtap="submitFeedback">
      <text wx:if="{{!submitting}}">提交反馈</text>
      <text wx:else>提交中...</text>
    </view>
  </view>

  <!-- 提示信息 -->
  <view class="tips-section">
    <view class="tips-title">温馨提示</view>
    <view class="tips-list">
      <view class="tip-item">• 我们会在1-3个工作日内回复您的反馈</view>
      <view class="tip-item">• 您的反馈对我们非常重要，感谢支持</view>
    </view>
  </view>
</view>