---
description: K12小程序项目解耦规则 - 防止功能间相互影响
globs: ["**/*.js", "**/*.wxml", "**/*.wxss"]
alwaysApply: true
---

# 📋 K12小程序项目解耦规则

## 🎯 核心原则

### 1. 接口独立原则
**每个页面/功能必须有独立的数据接口，严禁共用**

```javascript
// ❌ 错误示例 - 多个页面共用同一个接口
// utils/api.js
export const getFilesList = (params) => { /* 通用逻辑 */ }

// ✅ 正确示例 - 每个页面独立接口
// utils/api.js
export const getHomeFilesList = (params) => { /* 首页专用逻辑 */ }
export const getCategoryFilesList = (params) => { /* 分类页专用逻辑 */ }
export const getSearchFilesList = (params) => { /* 搜索页专用逻辑 */ }
```

### 2. 方法独立原则
**每个功能模块的方法必须独立，不允许跨页面调用**

```javascript
// ❌ 错误示例 - 跨页面调用方法
// pages/index/index.js
const categoryPage = require('../category/category.js')
categoryPage.loadFiles() // 禁止这样做

// ✅ 正确示例 - 每个页面独立实现
// pages/index/index.js
loadHomeFiles() { /* 首页独立实现 */ }

// pages/category/category.js  
loadCategoryFiles() { /* 分类页独立实现 */ }
```

### 3. 数据结构独立原则
**每个页面的数据结构必须独立定义，避免共享数据对象**

```javascript
// ❌ 错误示例 - 共享数据结构
// utils/dataStructure.js
export const fileItemStructure = { /* 通用结构 */ }

// ✅ 正确示例 - 独立数据结构
// pages/index/index.js
data: {
  homeFilesList: [], // 首页专用数据结构
}

// pages/category/category.js
data: {
  categoryFilesList: [], // 分类页专用数据结构
}
```

## 📁 文件组织规范

### 1. API接口文件组织
```
utils/
├── api/
│   ├── homeApi.js          # 首页专用接口
│   ├── categoryApi.js      # 分类页专用接口
│   ├── searchApi.js        # 搜索页专用接口
│   ├── detailApi.js        # 详情页专用接口
│   └── commonApi.js        # 仅限系统配置等真正通用的接口
```

### 2. 工具函数文件组织
```
utils/
├── helpers/
│   ├── homeHelpers.js      # 首页专用工具函数
│   ├── categoryHelpers.js  # 分类页专用工具函数
│   ├── searchHelpers.js    # 搜索页专用工具函数
│   └── commonHelpers.js    # 仅限格式化时间等真正通用的函数
```

### 3. 页面文件组织
```
pages/
├── index/
│   ├── index.js            # 首页逻辑（独立完整）
│   ├── indexService.js     # 首页业务逻辑服务（可选）
│   └── indexConfig.js      # 首页配置（可选）
├── category/
│   ├── category.js         # 分类页逻辑（独立完整）
│   ├── categoryService.js  # 分类页业务逻辑服务（可选）
│   └── categoryConfig.js   # 分类页配置（可选）
```

## 🔧 实现规范

### 1. 云数据库查询独立
```javascript
// ❌ 错误示例 - 共用查询方法
const getFiles = (collection, filters) => {
  return db.collection(collection).where(filters).get()
}

// ✅ 正确示例 - 独立查询方法
// homeApi.js
const getHomeFiles = () => {
  return db.collection('files')
    .where({ 
      status: 'active',
      featured: true  // 首页特有逻辑
    })
    .orderBy('sort_order', 'desc')
    .limit(10)
    .get()
}

// categoryApi.js  
const getCategoryFiles = (filters) => {
  return db.collection('files')
    .where({
      status: 'active',
      category: filters.category,  // 分类页特有逻辑
      grade: filters.grade,
      subject: filters.subject
    })
    .orderBy('created_time', 'desc')
    .get()
}
```

### 2. 数据处理方法独立
```javascript
// ❌ 错误示例 - 共用数据处理
const processFileData = (rawData) => { /* 通用处理 */ }

// ✅ 正确示例 - 独立数据处理
// homeHelpers.js
const processHomeFileData = (rawData) => {
  return rawData.map(item => ({
    id: item._id,
    title: item.title,
    coverImage: item.preview_images[0]?.url || '/images/default_cover.svg',
    downloadCount: item.download_count,
    // 首页特有的数据结构
    isFeatured: item.sort_order >= 500
  }))
}

// categoryHelpers.js
const processCategoryFileData = (rawData) => {
  return rawData.map(item => ({
    id: item._id,
    title: item.title,
    tags: [item.subject, item.grade].filter(Boolean),
    downloadCount: item.download_count,
    viewCount: item.view_count,
    // 分类页特有的数据结构
    fileInfo: {
      type: item.file_type.toUpperCase(),
      pages: item.pages,
      features: item.features
    }
  }))
}
```

### 3. 事件处理方法独立
```javascript
// ❌ 错误示例 - 共用事件处理
const handleItemClick = (e) => { /* 通用处理 */ }

// ✅ 正确示例 - 独立事件处理
// pages/index/index.js
handleHomeItemClick(e) {
  const fileId = e.currentTarget.dataset.id
  // 首页特有的跳转逻辑，可能需要传递特殊参数
  wx.navigateTo({
    url: `/pages/files-detail/files-detail?id=${fileId}&from=home`
  })
}

// pages/category/category.js
handleCategoryItemClick(e) {
  const fileId = e.currentTarget.dataset.id
  // 分类页特有的跳转逻辑，可能需要传递筛选条件
  wx.navigateTo({
    url: `/pages/files-detail/files-detail?id=${fileId}&from=category&grade=${this.data.selectedGrade}`
  })
}
```

## 🚫 严格禁止的行为

### 1. 禁止跨页面方法调用
```javascript
// ❌ 绝对禁止
const indexPage = getApp().globalData.pages.index
indexPage.loadFiles()

// ❌ 绝对禁止  
require('../other-page/other-page.js').someMethod()
```

### 2. 禁止共享页面级数据
```javascript
// ❌ 绝对禁止
getApp().globalData.sharedFilesList = []

// ❌ 绝对禁止
wx.setStorageSync('sharedData', data)
```

### 3. 禁止修改其他页面的接口
```javascript
// ❌ 绝对禁止 - 为了适配新页面而修改现有接口
// 原有接口：getHomeFiles()
// 错误做法：修改 getHomeFiles() 添加新参数来适配分类页

// ✅ 正确做法：新建独立接口
// 新建：getCategoryFiles()
```

## ✅ 允许的共享内容

### 1. 系统级配置
```javascript
// utils/config.js - 允许共享
export const APP_CONFIG = {
  pageSize: 20,
  maxRetryTimes: 3
}
```

### 2. 纯工具函数
```javascript
// utils/commonHelpers.js - 允许共享
export const formatTime = (timestamp) => { /* 纯函数 */ }
export const formatFileSize = (bytes) => { /* 纯函数 */ }
```

### 3. 云开发配置
```javascript
// utils/cloudConfig.js - 允许共享
export const initCloud = () => {
  wx.cloud.init({ env: 'your-env-id' })
}
```

## 🔍 代码审查检查点

### 开发前检查
- [ ] 是否为新功能创建了独立的API接口？
- [ ] 是否为新页面创建了独立的数据处理方法？
- [ ] 是否避免了修改现有页面的接口？

### 开发中检查  
- [ ] 是否有跨页面的方法调用？
- [ ] 是否有共享的页面级数据？
- [ ] 是否复用了其他页面的具体业务逻辑？

### 开发后检查
- [ ] 新功能是否影响了现有页面的数据？
- [ ] 是否可以独立测试新功能而不影响其他页面？
- [ ] 是否可以独立修改新功能而不影响其他页面？

## 📝 命名规范

### 1. API接口命名
```javascript
// 格式：get[页面名][具体功能]
getHomeFiles()           // 首页文件列表
getHomeBanners()         // 首页轮播图
getCategoryFiles()       // 分类页文件列表
getCategoryFilters()     // 分类页筛选选项
getSearchResults()       // 搜索结果
getSearchHotKeywords()   // 搜索热词
```

### 2. 数据处理方法命名
```javascript
// 格式：process[页面名][数据类型]
processHomeFileData()     // 首页文件数据处理
processCategoryFileData() // 分类页文件数据处理
processSearchResultData() // 搜索结果数据处理
```

### 3. 事件处理方法命名
```javascript
// 格式：handle[页面名][事件类型]
handleHomeItemClick()     // 首页项目点击
handleCategoryFilter()    // 分类页筛选
handleSearchInput()       // 搜索输入
```

## 🎯 实施建议

### 1. 渐进式重构
- 新功能严格按照此规则实现
- 现有功能出现问题时按此规则重构
- 不强制一次性重构所有现有代码

### 2. 团队协作
- 代码审查时重点检查是否违反解耦规则
- 发现违规行为立即指出并要求修改
- 定期回顾和完善解耦规则

### 3. 文档维护
- 每个页面维护独立的API文档
- 记录页面间的数据流向
- 更新时同步更新相关文档

---

**记住：宁可代码重复，也不要功能耦合！**
**独立开发，独立测试，独立部署，独立维护！**